/**
 * Security Configuration Utility
 *
 * Provides security configuration using local frontend configuration
 * instead of fetching from backend API to reduce dependencies
 */

import {
    SECURITY_PROTECTED_ROUTES,
    FALLBACK_URL_MAPPINGS,
    DEFAULT_FALLBACK_URL,
    SECURITY_CONFIG
} from '@/config/securityRoutes';

/**
 * Get security configuration (synchronous)
 * @returns {Object} Security configuration object
 */
export function getSecurityConfig() {
    return {
        cookie_name: SECURITY_CONFIG.cookie.name,
        expires_in_minutes: SECURITY_CONFIG.cookie.defaultExpirationMinutes,
        check_interval_seconds: SECURITY_CONFIG.cookie.checkIntervalSeconds,
        check_interval_milliseconds: SECURITY_CONFIG.cookie.checkIntervalSeconds * 1000,
        protected_routes: SECURITY_PROTECTED_ROUTES,
        referrer_control: {
            enabled: SECURITY_CONFIG.referrerControlEnabled,
            fallback_urls: FALLBACK_URL_MAPPINGS,
            default_fallback: DEFAULT_FALLBACK_URL,
        },
    };
}

/**
 * Fetch security configuration (async for backward compatibility)
 * @returns {Promise<Object>} Security configuration object
 */
export async function fetchSecurityConfig() {
    // Return the local configuration wrapped in a Promise for backward compatibility
    return Promise.resolve(getSecurityConfig());
}

/**
 * Get cached security configuration (synchronous)
 * @returns {Object} Security configuration object (always available since it's local)
 */
export function getCachedSecurityConfig() {
    // Since configuration is now local, always return the current config
    return getSecurityConfig();
}

/**
 * Clear the configuration cache (no-op since config is local)
 * @deprecated This function is kept for backward compatibility but does nothing
 */
export function clearSecurityConfigCache() {
    // No-op since we don't cache local configuration
}

/**
 * Get default security configuration
 * @returns {Object} Default configuration object
 */
export function getDefaultSecurityConfig() {
    // Return the same as getSecurityConfig since it's all local now
    return getSecurityConfig();
}

/**
 * Get cached protected routes (synchronous)
 * @returns {Array} Array of protected route paths
 */
export function getCachedProtectedRoutes() {
    // Since configuration is now local, directly return the protected routes
    return SECURITY_PROTECTED_ROUTES;
}

/**
 * Validate if a path is a valid secure route
 * @param {string} path - The path to validate
 * @returns {boolean} True if the path is a valid secure route
 */
export function isValidSecureRoute(path) {
    try {
        // Decode URL if it's encoded
        const decodedPath = decodeURIComponent(path);

        // Extract just the path part (remove query parameters for validation)
        const url = new URL(decodedPath, 'http://localhost');
        const pathOnly = url.pathname;

        // Remove leading slash if present for consistent comparison
        const normalizedPath = pathOnly.replace(/^\//, '');

        // Get protected routes directly from local config
        return SECURITY_PROTECTED_ROUTES.some(route => {
            // Remove leading slash from secure route for comparison
            const normalizedRoute = route.replace(/^\//, '');

            // Check if the path matches exactly or starts with the secure route path
            return normalizedPath === normalizedRoute || normalizedPath.startsWith(normalizedRoute);
        });
    } catch (error) {
        console.warn('Error validating secure route:', error);
        return false;
    }
}

/**
 * Get fallback URL for a secure page path
 * @param {string} securePagePath - The secure page path
 * @returns {string} Fallback URL
 */
export function getDirectAccessFallbackUrl(securePagePath) {
    // Use local configuration directly
    const fallbackUrl = FALLBACK_URL_MAPPINGS[securePagePath];
    if (fallbackUrl) {
        return fallbackUrl;
    }

    // Return default fallback if specific mapping not found
    return DEFAULT_FALLBACK_URL;
}
