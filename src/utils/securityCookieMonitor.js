/**
 * Enhanced Security Cookie Monitoring Utility
 *
 * Monitors security verification cookies on secure pages and automatically
 * redirects users when cookies become invalid or expire in real-time.
 */

import { getSecurityConfig, getCachedSecurityConfig, getDefaultSecurityConfig } from '@/utils/securityConfig';

class SecurityCookieMonitor {
    constructor() {
        this.intervalId = null;
        this.isMonitoring = false;
        this.checkInterval = 5000; // Default 5 seconds, will be updated from config
        this.cookieName = 'security_verified'; // Default name, will be updated from config
        this.debugMode = process.env.NODE_ENV === 'development';
        this.lastCookieValue = null;
        this.cookieSetTime = null;
        this.expectedExpiryTime = null;
        this.expiryWarningShown = false;
        this.configLoaded = false;
    }

    /**
     * Load security configuration from local config
     */
    loadSecurityConfig() {
        try {
            // Get configuration from local config (synchronous)
            const config = getSecurityConfig();

            // Update monitor settings with config
            this.checkInterval = config.check_interval_milliseconds || 5000;
            this.cookieName = config.cookie_name || 'security_verified';
            this.configLoaded = true;

            if (this.debugMode) {
                console.log('[SecurityCookieMonitor] Configuration loaded:', {
                    checkInterval: this.checkInterval,
                    cookieName: this.cookieName,
                    expiryMinutes: config.expires_in_minutes
                });
            }

            return config;
        } catch (error) {
            console.warn('[SecurityCookieMonitor] Failed to load config, using defaults:', error);

            // Use default configuration
            const defaultConfig = getDefaultSecurityConfig();
            this.checkInterval = defaultConfig.check_interval_milliseconds;
            this.cookieName = defaultConfig.cookie_name;
            this.configLoaded = true;

            return defaultConfig;
        }
    }

    /**
     * Start monitoring security cookies with expiration tracking
     */
    startMonitoring(expiryMinutes = 1) {
        if (this.isMonitoring) {
            return;
        }

        // Load configuration if not already loaded
        if (!this.configLoaded) {
            this.loadSecurityConfig();
        }

        this.isMonitoring = true;
        this.expiryWarningShown = false;

        // Track when monitoring starts and expected expiry
        this.cookieSetTime = new Date();
        this.expectedExpiryTime = new Date(this.cookieSetTime.getTime() + (expiryMinutes * 60 * 1000));

        // Initial check
        this.checkCookie();

        // Set up interval checking with dynamic interval
        this.intervalId = setInterval(() => {
            this.checkCookie();
        }, this.checkInterval);

        // Listen for page visibility changes
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

        // Listen for beforeunload to cleanup
        window.addEventListener('beforeunload', this.stopMonitoring.bind(this));

        // Set up expiry countdown
        this.setupExpiryCountdown(expiryMinutes);
    }

    /**
     * Set up countdown to expected expiry time
     */
    setupExpiryCountdown(expiryMinutes) {
        const expiryTime = expiryMinutes * 60 * 1000; // Convert to milliseconds
        
        // Check every second near expiry time
        const countdownInterval = setInterval(() => {
            const now = new Date();
            const timeElapsed = now.getTime() - this.cookieSetTime.getTime();
            const timeRemaining = expiryTime - timeElapsed;

            if (timeRemaining <= 0) {
                clearInterval(countdownInterval);
                this.checkCookie();
                return;
            }

            // Show warning when 30 seconds remain
            if (timeRemaining <= 30000 && !this.expiryWarningShown) {
                this.expiryWarningShown = true;
            }

        }, 1000); // Check every second

        // Store interval for cleanup
        this.countdownInterval = countdownInterval;
    }

    /**
     * Stop monitoring security cookies
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;

        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }

        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }

        // Remove event listeners
        document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
        window.removeEventListener('beforeunload', this.stopMonitoring.bind(this));
    }

    /**
     * Check cookie validity and handle redirects
     */
    checkCookie() {
        try {
            const cookieValue = this.getCookie(this.cookieName);
            const isValid = this.isValidSecurityCookie(cookieValue);

            // Check if cookie disappeared
            const cookieDisappeared = this.lastCookieValue && !cookieValue;
            
            // Check if cookie changed (possible tampering)
            const cookieChanged = this.lastCookieValue && cookieValue && this.lastCookieValue !== cookieValue;

            // Update last known cookie value
            this.lastCookieValue = cookieValue;

            if (!isValid || cookieDisappeared) {
                let reason = 'Invalid or missing security cookie';
                if (cookieDisappeared) reason = 'Security cookie was deleted or expired';
                if (cookieChanged) reason = 'Security cookie was modified';

                this.handleInvalidCookie(reason);
            }

        } catch (error) {
            this.handleInvalidCookie('Cookie check error: ' + error.message);
        }
    }

    /**
     * Handle invalid cookie by redirecting user
     */
    handleInvalidCookie(reason = 'Invalid or expired security cookie') {
        this.stopMonitoring();

        // Determine redirect URL with priority order:
        // 1. 'from' query parameter (if valid)
        // 2. document.referrer (if valid)
        // 3. fallback URL
        const fallbackUrl = '/account/overview';
        let redirectUrl = fallbackUrl;

        // Priority 1: Check for 'from' query parameter in current URL
        const fromParam = this.getFromParameter();
        if (fromParam && this.isValidInternalPath(fromParam)) {
            redirectUrl = fromParam;
            if (this.debugMode) {
                console.warn(`[SecurityCookieMonitor] ${reason} - Using 'from' parameter: ${redirectUrl}`);
            }
        } else {
            // Priority 2: Use referrer if it's from the same origin and not a security page
            const referrer = document.referrer;
            if (referrer && this.isValidReferrer(referrer)) {
                try {
                    const referrerUrl = new URL(referrer);
                    if (referrerUrl.origin === window.location.origin) {
                        redirectUrl = referrerUrl.pathname + referrerUrl.search + referrerUrl.hash;
                    }
                } catch (e) {
                    // Failed to parse referrer URL, use fallback
                }
            }

            // Show user-friendly message if in development
            if (this.debugMode) {
                const source = referrer && this.isValidReferrer(referrer) ? 'referrer' : 'fallback';
                console.warn(`[SecurityCookieMonitor] ${reason} - Using ${source}: ${redirectUrl}`);
            }
        }

        // Use Next.js router if available, otherwise use window.location
        if (typeof window !== 'undefined' && window.next && window.next.router) {
            window.next.router.push(redirectUrl);
        } else {
            window.location.href = redirectUrl;
        }
    }

    /**
     * Get the 'from' query parameter from current URL
     */
    getFromParameter() {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            const fromParam = urlParams.get('from');

            if (fromParam) {
                // Decode the parameter in case it's URL encoded
                return decodeURIComponent(fromParam);
            }

            return null;
        } catch (e) {
            // Failed to parse URL parameters
            return null;
        }
    }

    /**
     * Validate that a path is a valid internal path
     */
    isValidInternalPath(path) {
        if (!path || typeof path !== 'string') {
            return false;
        }

        // Must start with '/' to be an internal path
        if (!path.startsWith('/')) {
            return false;
        }

        // Don't allow external URLs (protocol schemes)
        if (path.includes('://') || path.includes('//')) {
            return false;
        }

        // Don't redirect back to security-related pages
        const securityPages = ['/security-check', '/login', '/signup', '/verify-email'];
        for (const page of securityPages) {
            if (path.startsWith(page)) {
                return false;
            }
        }

        // Don't allow dangerous characters that could be used for attacks
        const dangerousChars = ['<', '>', '"', "'", '&', 'javascript:', 'data:', 'vbscript:'];
        for (const char of dangerousChars) {
            if (path.toLowerCase().includes(char)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if referrer is valid for redirect
     */
    isValidReferrer(referrer) {
        if (!referrer) return false;

        // Don't redirect back to security-related pages
        const securityPages = ['/security-check', '/login', '/signup', '/verify-email'];

        for (const page of securityPages) {
            if (referrer.includes(page)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Handle page visibility changes
     */
    handleVisibilityChange() {
        if (document.hidden) {
            if (this.intervalId) {
                clearInterval(this.intervalId);
                this.intervalId = null;
            }
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval);
                this.countdownInterval = null;
            }
        } else {
            if (this.isMonitoring && !this.intervalId) {
                this.intervalId = setInterval(() => {
                    this.checkCookie();
                }, this.checkInterval);

                // Restart countdown if we have expiry time
                if (this.expectedExpiryTime) {
                    const now = new Date();
                    const timeRemaining = this.expectedExpiryTime.getTime() - now.getTime();
                    if (timeRemaining > 0) {
                        this.setupExpiryCountdown(timeRemaining / (60 * 1000)); // Convert back to minutes
                    }
                }
            }
        }
    }

    /**
     * Get cookie value by name
     */
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) {
            return parts.pop().split(';').shift();
        }
        return null;
    }

    /**
     * Validate security cookie (same logic as SecurityCheck component)
     */
    isValidSecurityCookie(cookieValue) {
        if (!cookieValue) return false;

        // Simple validation for 'true' value (legacy)
        if (cookieValue === 'true') return true;

        // Check if it's a session token (64 character hex string)
        if (typeof cookieValue === 'string' && cookieValue.length === 64 && /^[a-f0-9]+$/i.test(cookieValue)) {
            return true;
        }

        // Validate old encrypted payload format for backward compatibility
        try {
            const payload = JSON.parse(atob(cookieValue));
            if (payload.verified_at) {
                const verifiedAt = new Date(payload.verified_at);
                const now = new Date();
                const diffInSeconds = (now.getTime() - verifiedAt.getTime()) / 1000;
                return diffInSeconds <= 900; // 15 minutes buffer
            }
        } catch (e) {
            return false;
        }
        return false;
    }

    /**
     * Log messages (only in debug mode)
     */
    log(message, data = null) {
        if (this.debugMode) {
            if (data) {
                console.log(`[SecurityCookieMonitor] ${message}`, data);
            } else {
                console.log(`[SecurityCookieMonitor] ${message}`);
            }
        }
    }
}

// Create singleton instance
const securityCookieMonitor = new SecurityCookieMonitor();

export default securityCookieMonitor;
